{% load currency_filters %}
<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <title>{{ title }}</title>
    <style>
        @page {
            size: a4 portrait;
            margin: 1cm;
        }
        @import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap');

        body {
            font-family: 'Open Sans', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.5;
            color: {{ template.text_color|default:"#333333" }};
            background-color: {{ template.background_color|default:"#ffffff" }};
        }

        /* Professional report container */
        .report-container {
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        {% if is_preview %}
        /* Preview mode styles */
        .preview-banner {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background-color: #2563eb;
            color: white;
            text-align: center;
            padding: 10px;
            font-size: 14px;
            font-weight: bold;
            z-index: 1000;
        }

        .preview-actions {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: #f8f9fa;
            border-top: 1px solid #ddd;
            padding: 10px;
            text-align: center;
            z-index: 1000;
        }

        .preview-button {
            display: inline-block;
            padding: 8px 16px;
            margin: 0 5px;
            background-color: #2563eb;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
        }

        .preview-button.secondary {
            background-color: #6c757d;
        }

        body {
            margin-top: 50px;
            margin-bottom: 60px;
        }
        {% endif %}
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid {{ template.accent_color|default:"#1F3C88" }};
        }

        .header h1 {
            font-size: 24px;
            margin: 0;
            color: {{ template.header_color|default:"#1F3C88" }};
            font-weight: 700;
        }

        .header h2 {
            font-size: 18px;
            margin: 10px 0;
            color: {{ template.accent_color|default:"#1F3C88" }};
            font-weight: 600;
        }

        .header p {
            font-size: 14px;
            margin: 5px 0;
            color: {{ template.text_color|default:"#333333" }};
        }
        .summary {
            margin-bottom: 30px;
        }
        .summary-card {
            border: 1px solid {{ template.accent_color|default:"#1F3C88" }};
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: rgba(0, 0, 0, 0.02);
            text-align: center;
        }

        .summary-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
            color: {{ template.header_color|default:"#1F3C88" }};
        }

        .summary-value {
            font-size: 22px;
            font-weight: bold;
            color: {{ template.accent_color|default:"#1F3C88" }};
        }

        /* Dual currency styles for PDF */
        .currency-primary {
            font-weight: bold;
            display: block;
        }
        .currency-secondary {
            font-size: 10px;
            color: #666;
            font-weight: normal;
            display: block;
            margin-top: 2px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
            border: 1px solid #e0e0e0;
        }
        th {
            background-color: {{ template.table_header_color|default:"#f8f9fa" }};
            color: {{ template.text_color|default:"#333333" }};
            font-weight: 600;
            text-align: left;
            padding: 10px;
            border-bottom: 2px solid {{ template.accent_color|default:"#1F3C88" }};
            font-size: 12px;
        }

        td {
            padding: 10px;
            border-bottom: 1px solid #e0e0e0;
            font-size: 11px;
        }

        tr:nth-child(even) {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            color: {{ template.header_color|default:"#1F3C88" }};
            padding: 8px 0;
            border-bottom: 1px solid {{ template.accent_color|default:"#1F3C88" }};
        }
        .footer {
            text-align: center;
            font-size: 11px;
            color: {{ template.text_color|default:"#333333" }};
            margin-top: 40px;
            border-top: 1px solid #e0e0e0;
            padding-top: 15px;
            {% if not template.show_footer %}display: none;{% endif %}
        }
    </style>
</head>
<body>
    {% if is_preview %}
    <div class="preview-banner">
        Preview Mode - {{ format_type|upper }} Format
    </div>
    {% endif %}

    <div class="report-container">
        <div class="header">
            {% if template.show_logo %}
            <h1>LEGEND FITNESS</h1>
            {% endif %}
            <h2>{{ title }}</h2>
            <p>Period: {{ date_range }}</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <div class="summary-title">Total Income</div>
                <div class="summary-value">
                    <span class="currency-primary">{{ total_income|format_khr }}</span>
                    <span class="currency-secondary">{{ total_income|convert_khr_to_usd|format_usd }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Membership Payments -->
    <div class="section-title">Membership Payments ({{ membership_total|format_dual_currency }})</div>
    {% if membership_payments %}
    <table>
        <thead><tr>
                <th>Date</th>
                <th>Invoice No.</th>
                <th>Member</th>
                <th>Package</th>
                <th>Amount (KHR / USD)</th>
                <th>Payment Method</th>
                <th>Collected By</th>
            </tr>
        </thead>
        <tbody>
            {% for payment in membership_payments %}
            <tr>
                <td>{{ payment.payment_date|date:"d-M-Y" }}</td>
                <td>{{ payment.invoice_no }}</td>
                <td>{{ payment.member.name }}</td>
                <td>{{ payment.package.name }}</td>
                <td>
                    <span class="currency-primary">{{ payment.amount_khr|format_khr }}</span>
                    <span class="currency-secondary">{{ payment.amount_khr|convert_khr_to_usd|format_usd }}</span>
                </td>
                <td>{{ payment.get_payment_method_display }}</td>
                <td>{{ payment.collector.username }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <p>No membership payments found for this period.</p>
    {% endif %}

    <!-- Product Sales -->
    <div class="section-title">Product Sales ({{ product_total|format_dual_currency }})</div>
    {% if product_sales %}
    <table>
        <thead><tr>
                <th>Date</th>
                <th>Transaction ID</th>
                <th>Amount (KHR / USD)</th>
                <th>Payment Method</th>
                <th>Sold By</th>
            </tr>
        </thead>
        <tbody>
            {% for sale in product_sales %}
            <tr>
                <td>{{ sale.date|date:"d-M-Y" }}</td>
                <td>{{ sale.trxId }}</td>
                <td>
                    <span class="currency-primary">{{ sale.total_amount|format_khr }}</span>
                    <span class="currency-secondary">{{ sale.total_amount|convert_khr_to_usd|format_usd }}</span>
                </td>
                <td>{{ sale.get_payment_method_display }}</td>
                <td>{{ sale.sold_by.username }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <p>No product sales found for this period.</p>
    {% endif %}

    <!-- Pay-per-visit Revenue -->
    <div class="section-title">Pay-per-visit Revenue ({{ paypervisit_total|format_dual_currency }})</div>
    {% if paypervisit_revenue %}
    <table>
        <thead><tr>
                <th>Date</th>
                <th>Transaction ID</th>
                <th>Number of People</th>
                <th>Amount (KHR / USD)</th>
                <th>Payment Method</th>
                <th>Received By</th>
            </tr>
        </thead>
        <tbody>
            {% for visit in paypervisit_revenue %}
            <tr>
                <td>{{ visit.date|date:"d-M-Y" }}</td>
                <td>{{ visit.trxId }}</td>
                <td>{{ visit.num_people }}</td>
                <td>
                    <span class="currency-primary">{{ visit.amount|format_khr }}</span>
                    <span class="currency-secondary">{{ visit.amount|convert_khr_to_usd|format_usd }}</span>
                </td>
                <td>{{ visit.get_payment_method_display }}</td>
                <td>{{ visit.received_by.username }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <p>No pay-per-visit revenue found for this period.</p>
    {% endif %}

    <!-- Deposits -->
    <div class="section-title">Deposits ({{ deposit_total|format_dual_currency }})</div>
    {% if deposits %}
    <table>
        <thead><tr>
                <th>Date</th>
                <th>Transaction ID</th>
                <th>Description</th>
                <th>Amount (KHR / USD)</th>
                <th>Payment Method</th>
                <th>Created By</th>
            </tr>
        </thead>
        <tbody>
            {% for deposit in deposits %}
            <tr>
                <td>{{ deposit.transaction_date|date:"d-M-Y" }}</td>
                <td>{{ deposit.transaction_id }}</td>
                <td>{{ deposit.description|default:"Cash Deposit" }}</td>
                <td>
                    <span class="currency-primary">{{ deposit.amount_khr|format_khr }}</span>
                    <span class="currency-secondary">{{ deposit.amount_khr|convert_khr_to_usd|format_usd }}</span>
                </td>
                <td>{{ deposit.get_payment_method_display }}</td>
                <td>{{ deposit.staff.username }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <p>No deposits found for this period.</p>
    {% endif %}

    {% if template.show_footer %}
    <div class="footer">
        <p>Generated on {{ now|date:"d-M-Y H:i" }} | {{ template.footer_text|default:"Legend Fitness Club" }}</p>
    </div>
    {% endif %}

    {% if is_preview %}
    <div class="preview-actions">
        {% if format_type == 'pdf' %}
        <a href="{% url 'financialreport:export_pdf' 'income' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}&template={{ template.id }}" class="preview-button">
            <i class="fa-solid fa-download"></i> Download PDF
        </a>
        {% elif format_type == 'csv' %}
        <a href="{% url 'financialreport:export_csv' 'income' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}&template={{ template.id }}" class="preview-button">
            <i class="fa-solid fa-download"></i> Download CSV
        </a>
        {% elif format_type == 'print' %}
        <button onclick="window.print()" class="preview-button"><i class="fa-solid fa-print"></i> Print Report</button>
        {% endif %}
        <a href="javascript:window.close()" class="preview-button secondary">
            <i class="fa-solid fa-times"></i> Close Preview
        </a>
    </div>
    {% endif %}
</body>
</html>
